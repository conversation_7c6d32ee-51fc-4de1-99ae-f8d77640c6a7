"use client";
import React from "react";
import Image from "next/image";
import { InstagramIcon } from "@/app/ui/instagram";
import Button from "../ui/AnimatedButton";
import { LinkedinIcon } from "../ui/linkedin";
import { GithubIcon } from "../ui/github";
import SplashCursor from "../ui/SplashCursor";
const handleDownloadCV = () => {
  const link = document.createElement("a");
  link.href = "/asset/keshav_new.pdf";
  link.download = "keshavResume.pdf";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
const FirstPage = () => {
  return (
    <div className="min-h-screen ...">
      <>
        <SplashCursor />
        {/* Mobile View */}
        <div className="md:hidden min-h-screen w-full relative overflow-hidden animated-gradient-bg pt-20">
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50"></div>

          <div className="px-4 pb-8">
            {/* Profile Image Section */}
            <div className="w-[250px] h-[250px] mx-auto rounded-3xl overflow-hidden mb-8 relative">
              <Image
                src="/asset/Me.jpg"
                alt="Profile"
                fill
                sizes="(max-width: 640px) 250px, 100vw"
                className="object-cover object-top"
                priority
              />
              <div
                className="absolute top-4 right-4 bg-[#0a2a1c]/60 backdrop-blur-sm text-[#45d6e9]
                              px-4 py-2 rounded-full text-sm border border-[#45d6e9]/30 raleway-font"
              >
                Available for hire
              </div>
            </div>

            {/* Content Section */}
            <div className="space-y-4 hero-content">
              <p className="text-[#45d6e9] text-sm tracking-wider uppercase raleway-font text-reveal">
                WELCOME TO MY PORTFOLIO
              </p>

              <h1 className="text-4xl font-bold text-white raleway-font text-reveal">
                Hey, I&apos;m Keshav
              </h1>

              <div className="space-y-2">
                <h2 className="text-[#45d6e9] text-5xl font-bold custom text-reveal">
                  Transforming
                  <br />
                  Ideas into
                  <br />
                  code
                </h2>

                <p className="text-[#45d6e9] text-xl raleway-font">
                  Full Stack Web Developer
                </p>

                <p className="text-gray-400 text-lg leading-relaxed raleway-font">
                  Transforming ideas into elegant digital solutions. I
                  specialize in creating seamless, user-centric experiences that
                  blend innovation with functionality.
                </p>
              </div>

              {/* CTA Button */}
              <div className="pt-6">
                <Button
                  onClick={handleDownloadCV}
                  colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                  className="w-full bg-[#0a2a1c] border border-[#45d6e9]/30 raleway-font magnetic-btn"
                >
                  DOWNLOAD CV
                </Button>
              </div>

              {/* Social Icons - Mobile Version */}
              <div className="flex justify-center gap-8 pt-6">
                {[
                  {
                    Icon: LinkedinIcon,
                    link: "https://linkedin.com/in/its-keshavraj",
                    isCustom: true,
                  },
                  {
                    Icon: GithubIcon,
                    link: "https://github.com/itz-rajkeshav",
                    isCustom: true,
                  },
                  {
                    Icon: InstagramIcon,
                    link: "https://www.instagram.com/user.bot__/",
                    isCustom: true,
                  },
                ].map((item, index) => (
                  <a
                    key={index}
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:scale-110 transition-transform duration-300"
                  >
                    <item.Icon className="w-10 h-10 text-[#45d6e9] brightness-150" />
                  </a>
                ))}
              </div>
            </div>
          </div>

          <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1] pointer-events-none"></div>
        </div>

        {/* Tablet View */}
        <div className="hidden md:block lg:hidden min-h-screen w-full relative overflow-hidden animated-gradient-bg">
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50"></div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 pt-28 pb-16 flex flex-col-reverse items-center relative z-10 gap-8">
            <div className="flex-1 space-y-6 text-center">
              <p className="text-[#45d6e9] text-sm sm:text-md raleway-font tracking-wider">
                WELCOME TO MY PORTFOLIO
              </p>

              <h1 className="text-4xl sm:text-5xl font-bold text-white raleway-font">
                Hey, I&apos;m Keshav :)
              </h1>

              <div className="space-y-4">
                <h3 className="text-[#45d6e9] text-4xl sm:text-5xl font-bold custom">
                  Transforming Ideas
                  <br />
                  into Code
                </h3>

                <p className="text-[#45d6e9] text-xl raleway-font">
                  Full Stack Web Developer
                </p>

                <p className="text-gray-300 max-w-lg mx-auto raleway-font leading-relaxed">
                  I transform ideas into refined digital products, blending
                  innovative thinking with user-centric design to deliver
                  seamless and functional experiences.
                </p>

                <div className="flex flex-col items-center gap-6">
                  <Button
                    colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                    onClick={handleDownloadCV}
                  >
                    Download CV
                  </Button>

                  <div className="flex items-center gap-6">
                    {[
                      {
                        Icon: LinkedinIcon,
                        link: "https://linkedin.com/in/its-keshavraj",
                        isCustom: true,
                      },
                      {
                        Icon: GithubIcon,
                        link: "https://github.com/itz-rajkeshav",
                        isCustom: true,
                      },
                      {
                        Icon: InstagramIcon,
                        link: "https://www.instagram.com/user.bot__/",
                        isCustom: true,
                      },
                    ].map((item, index) => (
                      <a
                        key={index}
                        href={item.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`
                          hover:scale-110 transition-transform duration-300
                          ${!item.isCustom ? "hover:text-[#45d6e9]" : ""}
                        `}
                      >
                        <item.Icon className="text-[#45d6e9]" />
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Image */}
            <div className="w-[400px] h-[420px] mb-8">
              <div
                className="relative w-full h-full overflow-hidden rounded-3xl shadow-2xl shadow-[#45d6e9]/20
                           transform transition-all duration-500 ease-in-out
                           hover:shadow-[#45d6e9]/40 hover:shadow-3xl"
              >
                <Image
                  src="/asset/Me.jpg"
                  alt="Profile"
                  fill
                  sizes="(min-width: 640px) and (max-width: 1024px) 400px, 100vw"
                  className="object-cover object-top transition-transform duration-500 ease-in-out hover:scale-110"
                  priority
                />
                <div
                  className="absolute top-4 right-4 bg-[#45d6e9]/20 backdrop-blur-sm text-white
                                px-4 py-2 rounded-3xl raleway-font text-sm border border-[#45d6e9]/30
                                transition-all duration-500 ease-in-out
                                hover:bg-[#45d6e9]/30"
                >
                  Available for hire
                </div>
              </div>
            </div>
          </div>
          <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1]"></div>
        </div>

        {/* Desktop View remains the same */}
        <div className="hidden lg:block min-h-screen w-full relative overflow-hidden animated-gradient-bg">
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-50"></div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 sm:pt-32 pb-16 flex flex-col lg:flex-row justify-between items-center relative z-10 gap-12">
            {/* Left Content */}
            <div className="flex-1 space-y-6 text-center lg:text-left">
              <p className="text-[#45d6e9] text-sm sm:text-md raleway-font tracking-wider">
                WELCOME TO MY PORTFOLIO
              </p>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white raleway-font">
                Hey, I&apos;m Keshav :)
              </h1>

              <div className="space-y-4 sm:space-y-6">
                <h3 className="text-[#45d6e9] text-4xl sm:text-5xl lg:text-6xl font-bold custom">
                  Transforming Ideas
                  <br />
                  into Code
                </h3>

                <p className="text-[#45d6e9] text-xl raleway-font">
                  Full Stack Web Developer
                </p>

                <p className="text-gray-300 max-w-lg raleway-font leading-relaxed">
                  I transform ideas into refined digital products, blending
                  innovative thinking with user-centric design to deliver
                  seamless and functional experiences.
                </p>

                <div className="flex items-center space-x-14">
                  <Button
                    onClick={handleDownloadCV}
                    colors={["#45d6e9", "#0f92a9", "#45d6e9"]}
                  >
                    Download CV
                  </Button>

                  <div className="flex items-center gap-6">
                    {[
                      {
                        Icon: LinkedinIcon,
                        link: "https://linkedin.com/in/its-keshavraj",
                        isCustom: true,
                      },
                      {
                        Icon: GithubIcon,
                        link: "https://github.com/itz-rajkeshav",
                        isCustom: true,
                      },
                      {
                        Icon: InstagramIcon,
                        link: "https://www.instagram.com/user.bot__/",
                        isCustom: true,
                      },
                    ].map((item, index) => (
                      <a
                        key={index}
                        href={item.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`
                          hover:scale-110 transition-transform duration-300
                          ${!item.isCustom ? "hover:text-[#45d6e9]" : ""}
                        `}
                      >
                        <item.Icon className="text-[#45d6e9]" />
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content - Profile Image */}
            <div className="w-[450px] h-[470px]">
              <div
                className="relative w-full h-full overflow-hidden rounded-3xl shadow-2xl shadow-[#45d6e9]/20
                           transform transition-all duration-500 ease-in-out
                           hover:shadow-[#45d6e9]/40 hover:shadow-3xl"
              >
                <Image
                  src="/asset/Me.jpg"
                  alt="Profile"
                  fill
                  sizes="(min-width: 1024px) 450px, 100vw"
                  className="object-cover object-top transition-transform duration-500 ease-in-out hover:scale-110"
                  priority
                />
                <div
                  className="absolute top-4 right-4 bg-[#45d6e9]/20 backdrop-blur-sm text-white
                            px-4 py-2 rounded-3xl raleway-font text-sm border border-[#45d6e9]/30
                            transition-all duration-500 ease-in-out
                            hover:bg-[#45d6e9]/30"
                >
                  Available for hire
                </div>
              </div>
            </div>
          </div>
          <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.1]"></div>
        </div>
      </>
    </div>
  );
};

export default FirstPage;
