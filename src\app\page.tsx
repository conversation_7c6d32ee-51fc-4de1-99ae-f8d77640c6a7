"use client";
import "./globals.css";
import { lazy, Suspense } from "react";

const Navbar = lazy(() => import("./component/navbar"));
const FirstPage = lazy(() => import("./component/FirstPage"));
const SecondPage = lazy(() => import("./component/SecondPage"));
const ThirdPage = lazy(() => import("./component/ThirdPage"));
const SkillPage = lazy(() => import("./component/SkillAndJourney"));
const AnonymousWall = lazy(() => import("./component/AnonymousWall"));
const Contact = lazy(() => import("./component/Contact"));
const Footer = lazy(() => import("./component/Footer"));

export default function Home() {
  return (
    <Suspense>
      <Navbar />
      <FirstPage />
      <SecondPage />
      <ThirdPage />
      <SkillPage />
      <AnonymousWall />
      <Contact />
      <Footer />
    </Suspense>
  );
}
