"use client";
import "./globals.css";
import { lazy, Suspense } from "react";
import GSAPSmoothScrollProvider from "./components/GSAPSmoothScrollProvider";

const Navbar = lazy(() => import("./component/navbar"));
const FirstPage = lazy(() => import("./component/FirstPage"));
const SecondPage = lazy(() => import("./component/SecondPage"));
const ThirdPage = lazy(() => import("./component/ThirdPage"));
const SkillPage = lazy(() => import("./component/SkillAndJourney"));
const AnonymousWall = lazy(() => import("./component/AnonymousWall"));
const Contact = lazy(() => import("./component/Contact"));
const Footer = lazy(() => import("./component/Footer"));

export default function Home() {
  return (
    <GSAPSmoothScrollProvider
      duration={1.2}
      smoothTouch={true}
      touchMultiplier={2}
    >
      <Suspense>
        <div className="navbar">
          <Navbar />
        </div>
        <div className="scroll-section hero-content">
          <FirstPage />
        </div>
        <div className="scroll-section" id="about">
          <SecondPage />
        </div>
        <div className="scroll-section" id="projects">
          <ThirdPage />
        </div>
        <div className="scroll-section" id="skills">
          <SkillPage />
        </div>
        <div className="scroll-section" id="mysteryboard">
          <AnonymousWall />
        </div>
        <div className="scroll-section" id="contact">
          <Contact />
        </div>
        <div className="scroll-section">
          <Footer />
        </div>
      </Suspense>
    </GSAPSmoothScrollProvider>
  );
}
