"use client";
import "./globals.css";
import { lazy, Suspense } from "react";

const Navbar = lazy(() => import("./component/navbar"));
const FirstPage = lazy(() => import("./component/FirstPage"));
const SecondPage = lazy(() => import("./component/SecondPage"));
const ThirdPage = lazy(() => import("./component/ThirdPage"));
const SkillPage = lazy(() => import("./component/SkillAndJourney"));
const AnonymousWall = lazy(() => import("./component/AnonymousWall"));
const Contact = lazy(() => import("./component/Contact"));
const Footer = lazy(() => import("./component/Footer"));

export default function Home() {
  return (
    <Suspense>
      <Navbar />
      <div id="home">
        <FirstPage />
      </div>
      <div id="about">
        <SecondPage />
      </div>
      <div id="projects">
        <ThirdPage />
      </div>
      <div id="skills">
        <SkillPage />
      </div>
      <div id="mysteryboard">
        <AnonymousWall />
      </div>
      <div id="contact">
        <Contact />
      </div>
      <Footer />
    </Suspense>
  );
}
