"use client";
import React, { useState } from "react";
import { LinkedinIcon } from "../ui/linkedin";
import { GithubIcon } from "../ui/github";
import { InstagramIcon } from "../ui/instagram";
import { useGSAPSmoothScrollContext } from "../components/GSAPSmoothScrollProvider";

const FooterNavItem = ({
  text,
  sectionId,
}: {
  text: string;
  sectionId: string;
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const { scrollToElement } = useGSAPSmoothScrollContext();

  const scrollToSection = (sectionId: string) => {
    scrollToElement(`#${sectionId}`, {
      offset: 80,
      duration: 1.2,
      easing: "easeInOutQuart",
    });
  };

  return (
    <div
      className="relative px-4 py-2 rounded-md group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      onClick={() => scrollToSection(sectionId)}
    >
      <div
        className={`absolute inset-0 rounded-md transition-all duration-300 ease-in-out ${
          isHovered || isPressed ? "opacity-100" : "opacity-0"
        }`}
      >
        <div
          className="absolute inset-0 opacity-50 blur-xl"
          style={{
            background: "linear-gradient(90deg, #45d6e9, #0f92a9, #45d6e9)",
          }}
        />
        <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:4px_4px] opacity-[0.1]" />
      </div>
      <span className="relative z-10 font-medium transition-colors duration-300 ease-in-out group-hover:text-[#45d6e9] text-white/80 raleway-font">
        {text}
      </span>
    </div>
  );
};

const Footer = () => {
  return (
    <footer className="relative animated-gradient-bg py-12 px-6 overflow-hidden">
      <div className="container mx-auto max-w-6xl relative z-10">
        {/* Mobile and Tablet Layout (everything below lg) */}
        <div className="flex flex-col space-y-8 lg:hidden">
          {/* Brand Section */}
          <div className="text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#45d6e9] mb-2 custom">
              Keshav.dev
            </h2>
            <p className="text-white/60 max-w-xs mx-auto sm:max-w-sm raleway-font">
              Crafting unique web experiences for brands and businesses.
            </p>
          </div>

          {/* Quick Links Section */}
          <div className="flex flex-col items-center">
            <h3 className="text-xl sm:text-2xl font-medium text-[#45d6e9] mb-4 custom text-center">
              Quick Links
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              <FooterNavItem text="About" sectionId="about" />
              <FooterNavItem text="Projects" sectionId="projects" />
              <FooterNavItem text="Skills" sectionId="skills" />
              <FooterNavItem text="Contact" sectionId="contact" />
            </div>
          </div>

          {/* Follow Me Section */}
          <div>
            <h3 className="text-xl sm:text-2xl font-medium text-[#45d6e9] mb-4 custom text-center">
              Follow Me
            </h3>
            <div className="flex justify-center gap-6 sm:gap-8">
              {[
                {
                  Icon: LinkedinIcon,
                  link: "https://linkedin.com/in/its-keshavraj",
                },
                {
                  Icon: GithubIcon,
                  link: "https://github.com/itz-rajkeshav",
                },
                {
                  Icon: InstagramIcon,
                  link: "https://www.instagram.com/user.bot__/",
                },
              ].map((item, index) => (
                <a
                  key={index}
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:scale-110 transition-transform duration-300"
                >
                  <item.Icon className="w-10 h-10 sm:w-12 sm:h-12 text-[#45d6e9] brightness-150" />
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Desktop Layout (lg and above) */}
        <div className="hidden lg:grid lg:grid-cols-3 gap-8">
          {/* Left section */}
          <div className="flex flex-col items-center justify-end">
            <h2 className="text-2xl font-bold text-[#45d6e9] mb-2 custom">
              Keshav.dev
            </h2>
            <p className="text-white/80 max-w-xs text-center raleway-font">
              Crafting unique web experiences for brands and businesses.
            </p>
          </div>

          {/* Middle section */}
          <div className="flex flex-col items-center justify-center">
            <h3 className="text-xl font-medium text-[#45d6e9] mb-4 custom">
              Quick Links
            </h3>
            <div className="flex items-center space-x-4">
              <FooterNavItem text="About" sectionId="about" />
              <FooterNavItem text="Projects" sectionId="projects" />
              <FooterNavItem text="Skills" sectionId="skills" />
              <FooterNavItem text="Contact" sectionId="contact" />
            </div>
          </div>

          {/* Right section */}
          <div className="flex flex-col items-center justify-center">
            <div className="flex flex-col items-center">
              <h3 className="text-xl font-medium text-[#45d6e9] mb-4 custom">
                Follow Me
              </h3>
              <div className="flex justify-center gap-6">
                {[
                  {
                    Icon: LinkedinIcon,
                    link: "https://linkedin.com/in/its-keshavraj",
                  },
                  {
                    Icon: GithubIcon,
                    link: "https://github.com/itz-rajkeshav",
                  },
                  {
                    Icon: InstagramIcon,
                    link: "https://www.instagram.com/user.bot__/",
                  },
                ].map((item, index) => (
                  <a
                    key={index}
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:scale-110 transition-transform duration-300"
                  >
                    <item.Icon className="w-12 h-12 text-[#45d6e9] brightness-150" />
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center text-white/60 mt-12 pt-8 border-t border-[#45d6e9]/10 raleway-font">
          <p>
            © 2025 Made with ❤️ by{" "}
            <span className="text-[#45d6e9] custom">Keshav</span>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
