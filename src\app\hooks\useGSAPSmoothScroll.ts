"use client";
import { useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Lenis from 'lenis';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface GSAPSmoothScrollOptions {
  duration?: number;
  easing?: string;
  smooth?: number;
  direction?: 'vertical' | 'horizontal';
  gestureDirection?: 'vertical' | 'horizontal' | 'both';
  smoothTouch?: boolean;
  touchMultiplier?: number;
}

export const useGSAPSmoothScroll = (options: GSAPSmoothScrollOptions = {}) => {
  const lenisRef = useRef<Lenis | null>(null);
  const rafRef = useRef<number | null>(null);

  const {
    duration = 1.2,
    easing = 'easeOutExpo',
    smooth = 1,
    direction = 'vertical',
    gestureDirection = 'vertical',
    smoothTouch = true,
    touchMultiplier = 2
  } = options;

  // Initialize Lenis smooth scroll (like pritambose.netlify.app)
  const initializeLenis = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Create Lenis instance with pritambose.netlify.app settings
    lenisRef.current = new Lenis({
      duration: duration,
      easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // easeOutExpo
      direction: direction,
      gestureDirection: gestureDirection,
      smooth: true,
      smoothTouch: smoothTouch,
      touchMultiplier: touchMultiplier,
      infinite: false,
      autoResize: true,
      // Advanced settings for premium feel
      normalizeWheel: true,
      wheelMultiplier: 1,
      touchInertiaMultiplier: 35,
    });

    // Connect Lenis with GSAP ScrollTrigger
    lenisRef.current.on('scroll', (e: any) => {
      ScrollTrigger.update();
    });

    // Animation loop
    const raf = (time: number) => {
      lenisRef.current?.raf(time);
      rafRef.current = requestAnimationFrame(raf);
    };
    rafRef.current = requestAnimationFrame(raf);

    // Update ScrollTrigger on resize
    const handleResize = () => {
      ScrollTrigger.refresh();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      lenisRef.current?.destroy();
      window.removeEventListener('resize', handleResize);
    };
  }, [duration, easing, smooth, direction, gestureDirection, smoothTouch, touchMultiplier]);

  // Smooth scroll to element (pritambose.netlify.app style)
  const scrollToElement = useCallback((target: string | HTMLElement, options: {
    offset?: number;
    duration?: number;
    easing?: string;
  } = {}) => {
    if (!lenisRef.current) return;

    const {
      offset = 0,
      duration: scrollDuration = 1.5,
      easing: scrollEasing = 'easeInOutQuart'
    } = options;

    const targetElement = typeof target === 'string' 
      ? document.querySelector(target) as HTMLElement
      : target;

    if (!targetElement) return;

    // Get target position
    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;

    // Use Lenis scrollTo for smooth animation
    lenisRef.current.scrollTo(targetPosition, {
      duration: scrollDuration,
      easing: (t: number) => {
        // Custom easing functions like pritambose.netlify.app
        switch (scrollEasing) {
          case 'easeInOutQuart':
            return t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2;
          case 'easeOutExpo':
            return t === 1 ? 1 : 1 - Math.pow(2, -10 * t);
          case 'easeInOutCubic':
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
          default:
            return t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2;
        }
      }
    });
  }, []);

  // Scroll to top with pritambose.netlify.app animation
  const scrollToTop = useCallback((duration: number = 1.5) => {
    if (!lenisRef.current) return;

    lenisRef.current.scrollTo(0, {
      duration: duration,
      easing: (t: number) => 1 - Math.pow(1 - t, 4) // easeOutQuart
    });
  }, []);

  // Start/stop smooth scroll
  const start = useCallback(() => {
    lenisRef.current?.start();
  }, []);

  const stop = useCallback(() => {
    lenisRef.current?.stop();
  }, []);

  // Get current scroll progress
  const getScrollProgress = useCallback(() => {
    if (!lenisRef.current) return 0;
    return lenisRef.current.progress || 0;
  }, []);

  // Initialize on mount
  useEffect(() => {
    const cleanup = initializeLenis();
    return cleanup;
  }, [initializeLenis]);

  return {
    lenis: lenisRef.current,
    scrollToElement,
    scrollToTop,
    start,
    stop,
    getScrollProgress
  };
};

// GSAP ScrollTrigger animations (pritambose.netlify.app style)
export const initializeGSAPAnimations = () => {
  if (typeof window === 'undefined') return;

  // Fade in animations for sections
  gsap.utils.toArray('.scroll-section').forEach((section: any) => {
    gsap.fromTo(section, 
      {
        opacity: 0,
        y: 100,
      },
      {
        opacity: 1,
        y: 0,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        }
      }
    );
  });

  // Parallax effects for backgrounds
  gsap.utils.toArray('.parallax-bg').forEach((bg: any) => {
    gsap.fromTo(bg,
      {
        yPercent: -50
      },
      {
        yPercent: 50,
        ease: "none",
        scrollTrigger: {
          trigger: bg,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      }
    );
  });

  // Text reveal animations
  gsap.utils.toArray('.text-reveal').forEach((text: any) => {
    gsap.fromTo(text,
      {
        opacity: 0,
        y: 50,
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: text,
          start: "top 85%",
          toggleActions: "play none none reverse",
        }
      }
    );
  });

  // Stagger animations for cards/items
  gsap.utils.toArray('.stagger-item').forEach((item: any, index: number) => {
    gsap.fromTo(item,
      {
        opacity: 0,
        y: 60,
        scale: 0.9
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: "power3.out",
        delay: index * 0.1,
        scrollTrigger: {
          trigger: item,
          start: "top 85%",
          toggleActions: "play none none reverse",
        }
      }
    );
  });
};

// Utility to refresh ScrollTrigger
export const refreshScrollTrigger = () => {
  if (typeof window !== 'undefined') {
    ScrollTrigger.refresh();
  }
};
