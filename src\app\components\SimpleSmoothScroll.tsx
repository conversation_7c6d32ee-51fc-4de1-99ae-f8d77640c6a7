"use client";
import React, { useEffect, useRef, ReactNode } from 'react';
import Lenis from 'lenis';

interface SimpleSmoothScrollProps {
  children: ReactNode;
  duration?: number;
  easing?: (t: number) => number;
  smoothTouch?: boolean;
}

const SimpleSmoothScroll: React.FC<SimpleSmoothScrollProps> = ({
  children,
  duration = 1.2,
  easing = (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // easeOutExpo
  smoothTouch = true
}) => {
  const lenisRef = useRef<Lenis | null>(null);
  const rafRef = useRef<number | null>(null);

  useEffect(() => {
    // Initialize Lenis smooth scroll
    lenisRef.current = new Lenis({
      duration: duration,
      easing: easing,
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      smoothTouch: smoothTouch,
      touchMultiplier: 2,
      infinite: false,
      autoResize: true,
      normalizeWheel: true,
      wheelMultiplier: 1,
      touchInertiaMultiplier: 35,
    });

    // Animation loop
    const raf = (time: number) => {
      lenisRef.current?.raf(time);
      rafRef.current = requestAnimationFrame(raf);
    };
    rafRef.current = requestAnimationFrame(raf);

    // Cleanup function
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      lenisRef.current?.destroy();
    };
  }, [duration, easing, smoothTouch]);

  // Global scroll to function
  useEffect(() => {
    // Add global scroll function to window
    (window as any).smoothScrollTo = (target: string | HTMLElement, offset: number = 80) => {
      if (!lenisRef.current) return;

      const targetElement = typeof target === 'string' 
        ? document.querySelector(target) as HTMLElement
        : target;

      if (!targetElement) return;

      const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;

      lenisRef.current.scrollTo(targetPosition, {
        duration: 1.2,
        easing: (t: number) => t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2 // easeInOutQuart
      });
    };

    // Cleanup
    return () => {
      delete (window as any).smoothScrollTo;
    };
  }, []);

  return (
    <div 
      className="smooth-scroll-container"
      style={{
        // Basic optimizations
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden'
      }}
    >
      {children}
      
      {/* Basic CSS for smooth scrolling */}
      <style jsx global>{`
        html {
          scrollbar-width: none;
          -ms-overflow-style: none;
        }
        
        html::-webkit-scrollbar {
          display: none;
        }
        
        .lenis {
          height: 100vh;
          overflow: hidden;
        }
        
        .lenis.lenis-smooth {
          scroll-behavior: auto;
        }
        
        .lenis.lenis-smooth [data-lenis-prevent] {
          overscroll-behavior: contain;
        }
        
        /* Basic performance optimizations */
        .smooth-scroll-container {
          overflow-x: hidden;
        }
        
        .smooth-scroll-container * {
          transform: translateZ(0);
          backface-visibility: hidden;
        }
      `}</style>
    </div>
  );
};

export default SimpleSmoothScroll;
