"use client";
import React, { useEffect, useRef, ReactNode, createContext, useContext } from 'react';
import { useGSAPSmoothScroll, initializeGSAPAnimations } from '../hooks/useGSAPSmoothScroll';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface GSAPSmoothScrollContextType {
  scrollToElement: (target: string | HTMLElement, options?: any) => void;
  scrollToTop: (duration?: number) => void;
  start: () => void;
  stop: () => void;
  getScrollProgress: () => number;
}

const GSAPSmoothScrollContext = createContext<GSAPSmoothScrollContextType | null>(null);

export const useGSAPSmoothScrollContext = () => {
  const context = useContext(GSAPSmoothScrollContext);
  if (!context) {
    throw new Error('useGSAPSmoothScrollContext must be used within GSAPSmoothScrollProvider');
  }
  return context;
};

interface GSAPSmoothScrollProviderProps {
  children: ReactNode;
  duration?: number;
  easing?: string;
  smooth?: number;
  smoothTouch?: boolean;
  touchMultiplier?: number;
}

const GSAPSmoothScrollProvider: React.FC<GSAPSmoothScrollProviderProps> = ({
  children,
  duration = 1.2,
  easing = 'easeOutExpo',
  smooth = 1,
  smoothTouch = true,
  touchMultiplier = 2
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollToElement, scrollToTop, start, stop, getScrollProgress } = useGSAPSmoothScroll({
    duration,
    easing,
    smooth,
    smoothTouch,
    touchMultiplier
  });

  useEffect(() => {
    // Initialize GSAP animations after component mounts
    const initAnimations = () => {
      // Wait for DOM to be ready
      setTimeout(() => {
        initializeGSAPAnimations();
        
        // Custom animations for portfolio sections
        initializePortfolioAnimations();
      }, 100);
    };

    initAnimations();

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  // Portfolio-specific animations (pritambose.netlify.app style)
  const initializePortfolioAnimations = () => {
    // Hero section animation
    gsap.fromTo('.hero-content', 
      {
        opacity: 0,
        y: 100,
      },
      {
        opacity: 1,
        y: 0,
        duration: 1.5,
        ease: "power3.out",
        delay: 0.2
      }
    );

    // Navbar animation on scroll
    ScrollTrigger.create({
      start: "top -80",
      end: 99999,
      toggleClass: {
        className: "scrolled",
        targets: ".navbar"
      }
    });

    // Skills/cards stagger animation
    gsap.utils.toArray('.skill-card, .project-card').forEach((card: any, index: number) => {
      gsap.fromTo(card,
        {
          opacity: 0,
          y: 80,
          scale: 0.8
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1,
          ease: "power3.out",
          delay: index * 0.1,
          scrollTrigger: {
            trigger: card,
            start: "top 85%",
            toggleActions: "play none none reverse",
          }
        }
      );
    });

    // Text reveal with split text effect
    gsap.utils.toArray('.text-reveal').forEach((text: any) => {
      const chars = text.textContent.split('');
      text.innerHTML = chars.map((char: string) => 
        `<span class="char" style="display: inline-block;">${char === ' ' ? '&nbsp;' : char}</span>`
      ).join('');

      gsap.fromTo(text.querySelectorAll('.char'),
        {
          opacity: 0,
          y: 50,
          rotationX: -90
        },
        {
          opacity: 1,
          y: 0,
          rotationX: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.02,
          scrollTrigger: {
            trigger: text,
            start: "top 80%",
            toggleActions: "play none none reverse",
          }
        }
      );
    });

    // Parallax backgrounds
    gsap.utils.toArray('.parallax-bg').forEach((bg: any) => {
      gsap.fromTo(bg,
        {
          yPercent: -30
        },
        {
          yPercent: 30,
          ease: "none",
          scrollTrigger: {
            trigger: bg.parentElement,
            start: "top bottom",
            end: "bottom top",
            scrub: 1
          }
        }
      );
    });

    // Image reveal animations
    gsap.utils.toArray('.image-reveal').forEach((img: any) => {
      gsap.fromTo(img,
        {
          clipPath: "inset(100% 0% 0% 0%)",
          scale: 1.3
        },
        {
          clipPath: "inset(0% 0% 0% 0%)",
          scale: 1,
          duration: 1.5,
          ease: "power3.out",
          scrollTrigger: {
            trigger: img,
            start: "top 80%",
            toggleActions: "play none none reverse",
          }
        }
      );
    });

    // Counter animations
    gsap.utils.toArray('.counter').forEach((counter: any) => {
      const target = parseInt(counter.dataset.target || '0');
      gsap.fromTo(counter,
        { textContent: 0 },
        {
          textContent: target,
          duration: 2,
          ease: "power2.out",
          snap: { textContent: 1 },
          scrollTrigger: {
            trigger: counter,
            start: "top 80%",
            toggleActions: "play none none reverse",
          }
        }
      );
    });

    // Magnetic effect for buttons (pritambose.netlify.app style)
    gsap.utils.toArray('.magnetic-btn').forEach((btn: any) => {
      const handleMouseMove = (e: MouseEvent) => {
        const rect = btn.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        gsap.to(btn, {
          x: x * 0.3,
          y: y * 0.3,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      const handleMouseLeave = () => {
        gsap.to(btn, {
          x: 0,
          y: 0,
          duration: 0.5,
          ease: "power2.out"
        });
      };

      btn.addEventListener('mousemove', handleMouseMove);
      btn.addEventListener('mouseleave', handleMouseLeave);
    });
  };

  const contextValue = {
    scrollToElement,
    scrollToTop,
    start,
    stop,
    getScrollProgress
  };

  return (
    <GSAPSmoothScrollContext.Provider value={contextValue}>
      <div 
        ref={containerRef}
        className="gsap-smooth-scroll-container"
        style={{
          // Optimize for smooth scrolling
          willChange: 'transform',
          backfaceVisibility: 'hidden',
          perspective: '1000px'
        }}
      >
        {children}
        
        {/* Add global styles for GSAP animations */}
        <style jsx global>{`
          .gsap-smooth-scroll-container {
            overflow-x: hidden;
          }
          
          .scroll-section {
            will-change: transform, opacity;
          }
          
          .navbar.scrolled {
            backdrop-filter: blur(20px);
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 1px solid rgba(69, 214, 233, 0.1);
          }
          
          .char {
            transform-origin: center bottom;
          }
          
          .magnetic-btn {
            cursor: pointer;
            transition: transform 0.3s ease;
          }
          
          .image-reveal {
            overflow: hidden;
          }
          
          .parallax-bg {
            will-change: transform;
          }
          
          /* Smooth scrollbar styling */
          ::-webkit-scrollbar {
            display: none;
          }
          
          html {
            scrollbar-width: none;
            -ms-overflow-style: none;
          }
        `}</style>
      </div>
    </GSAPSmoothScrollContext.Provider>
  );
};

export default GSAPSmoothScrollProvider;
